/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.service.impl;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.Nutrients;
import com.app.cargill.constants.SortOrder;
import com.app.cargill.dairymax.model.DairyMax;
import com.app.cargill.dairymax.model.MaxDiet;
import com.app.cargill.dairymax.service.IDairyMaxService;
import com.app.cargill.document.DietDocument;
import com.app.cargill.document.DietOptimizationNutrient;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.IPensService;
import com.app.cargill.service.ISiteService;
import com.app.cargill.service.data.MergeSitesService;
import com.app.cargill.utils.PageableUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service("dairyMaxServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"})
public class DairyMaxServiceImpl implements IDairyMaxService {

  private final SiteMappingsRepository siteMappingsRepository;
  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;
  private final DietRepository dietRepository;
  private final PensRepository penRepository;
  private final IPensService pensService;
  private final ISiteService siteService;
  private final MergeSitesService mergeSitesService;

  @Override
  public void updateSiteFromMax(List<DairyMax> data) throws CustomDEExceptions {

    log.debug("[DairyMaxServiceImpl][updateSiteFromMax] Entering...");

    List<SiteMappings> mappings;
    SiteMappings mapping;
    Accounts account;
    List<Diets> diets;
    Sites site;
    List<Pens> pens;

    log.debug("[DairyMaxServiceImpl][updateSiteFromMax] payload size: " + data.size());

    for (DairyMax max : data) {
      if (max.getMaxOrigination() != null
          && max.getMaxOrigination().equalsIgnoreCase(DietSource.MAX.name())
          && max.getMaxDataSourceMappings() != null
          && !max.getMaxDataSourceMappings().isEmpty()
          && max.getMaxDataSourceMappings().get(0).getSystemId() != null
          && !max.getMaxDataSourceMappings().get(0).getSystemId().isBlank()) {

        String dietId = null;
        if (max.getMaxDiets() != null
            && max.getMaxDiets().get(0) != null
            && max.getMaxDiets().get(0).getMaxId() != null) {
          dietId = max.getMaxDiets().get(0).getMaxId().toString();
        }
        String maxSiteId = max.getMaxDataSourceMappings().get(0).getSystemId();
        Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
        try {
          log.debug("[DairyMaxServiceImpl][updateSiteFromMax] Processing Max Site: " + maxSiteId);

          mappings = siteMappingsRepository.findByDairyMaxSiteId(maxSiteId, pageable);

          mapping = mappings.get(0);

          if (dietId != null) {
            checkMaxSiteIdTransferFromOneSiteToAnother(
                maxSiteId, dietId, mapping.getSiteMappingDocument().getLabyrinthSiteId());
          }

          log.debug(
              "[DairyMaxServiceImpl][updateSiteFromMax] mapping: "
                  + mapping.getId()
                  + " fetched for max site id: "
                  + maxSiteId);

          account =
              accountsRepository.findByAccountId(
                  mapping.getSiteMappingDocument().getLabyrinthAccountId().toString());

          log.debug(
              "[DairyMaxServiceImpl][updateSiteFromMax] Account fetched : "
                  + account.getAccountDocument().getId().toString());

          site =
              sitesRepository.findBySiteId(
                  mapping.getSiteMappingDocument().getLabyrinthSiteId().toString());

          log.debug(
              "[DairyMaxServiceImpl][updateSiteFromMax] Site fetched : "
                  + site.getSiteDocument().getId().toString());

          diets =
              ListUtils.emptyIfNull(
                  dietRepository.findAllBySiteId(site.getSiteDocument().getId().toString()));

          log.debug("[DairyMaxServiceImpl][updateSiteFromMax] Diets fetched: " + diets.size());

          pens =
              ListUtils.emptyIfNull(
                  penRepository.findBySiteId(site.getSiteDocument().getId().toString()));

        } catch (NullPointerException | IndexOutOfBoundsException ex) {
          log.error(
              "[DairyMaxServiceImpl][updateSiteFromMax] skipping record because : "
                  + ex.getMessage());
          continue;
        }
        try {

          processDiets(diets, max, account, site, pens);

          site.getSiteDocument()
              .setNetEnergyOfLactationDairy(pensService.getNetEnergyOfLactationDairy(pens));
          site.getSiteDocument().setLastModifiedTimeUtc(Instant.now());

          siteService.updateSiteMappingsMax(site, mapping.getSiteMappingDocument());

          log.debug("[DairyMaxServiceImpl][updateSiteFromMax] site mappings updated");

          site.getSiteDocument().setLastSyncTimeUtc(Instant.now());

          account.getAccountDocument().setLastModifiedTimeUtc(Instant.now());

          accountsRepository.save(account);
          log.debug("[DairyMaxServiceImpl][updateSiteFromMax] account updated.");
          sitesRepository.save(site);
          log.debug("[DairyMaxServiceImpl][updateSiteFromMax] site updated.");
        } catch (Exception ex) {
          log.error("[DairyMaxServiceImpl][updateSiteFromMax] " + ex.getMessage());
          throw new CustomDEExceptions(
              "An error has occurred while processing the request. Please try later.",
              HttpStatus.BAD_REQUEST.value());
        }
      }
    }
  }

  private void checkMaxSiteIdTransferFromOneSiteToAnother(
      String maxSiteId, String dietId, UUID newSiteId) {

    log.info("Checking if Diet needs to transfered from one site to another, Diet ID: {}", dietId);
    Diets diet = dietRepository.findById(dietId);
    if (diet != null) {
      if (!diet.getDietDocument().getSiteId().equals(newSiteId)) {
        mergeSitesService.transferDietFromOneSiteToAnother(
            UUID.fromString(maxSiteId), diet.getDietDocument().getSiteId());

        log.info(
            "diets transferred successfully from site 1 {} to site 2 {}",
            diet.getDietDocument().getSiteId(),
            newSiteId);
      } else {
        log.info("No need to transfer diet, SiteMapping and diets have the same site ID.");
      }

    } else {
      log.info("Diet not found, either is deleted or not in db, diet ID: {}", dietId);
    }
  }

  //  @Override
  //  public List<DairyMax> getSitesToMax(Date from) throws CustomDEExceptions {
  //    List<DairyMax> maxSites = new ArrayList<>();
  //    try {
  //      List<Sites> sites = sitesRepository.findByUpdatedDate(from);
  //      List<String> siteIds =
  //          sites.stream().map(Sites::getSiteDocument).toList().parallelStream()
  //              .map(SiteDocument::getId)
  //              .toList()
  //              .stream()
  //              .map(UUID::toString)
  //              .toList();
  //      List<Pens> allPens = penRepository.findBySiteIds(siteIds);
  //      List<Diets> allDiets = dietRepository.findBySiteIds(siteIds);
  //
  //      for (Sites site : sites) {
  //        List<DietDocument> tempDiets = new ArrayList<>();
  //        DairyMax maxSite = new DairyMax(site.getSiteDocument());
  //
  //        List<PenDocument> pens = setupSitePens(allPens, site);
  //
  //        setBarnsForMaxSite(pens, maxSite);
  //
  //        List<DietDocument> diets = initializeSiteDiets(pens, site, allDiets);
  //
  //        if (maxSite.getMaxDiets() == null) {
  //          maxSite.setMaxDiets(new ArrayList<>());
  //        }
  //
  //        diets.forEach(d -> maxSite.getMaxDiets().add(new MaxDiet(d)));
  //
  //        List<UUID> mappedPenIds = pens.stream().map(PenDocument::getId).toList();
  //
  //        for (DietDocument diet : diets) {
  //          maxSite.getMaxDiets().add(new MaxDiet(diet));
  //
  //          if (diet.getSelectedPenGuids() == null) {
  //            diet.setSelectedPenGuids(new ArrayList<>());
  //          }
  //          diet.getSelectedPenGuids().addAll(mappedPenIds);
  //
  //          if (diet.getSource() == DietSource.MAX
  //              && diet.getFormulateOptimization() == null
  //              && diet.getAnalyzeOptimization() == null) {
  //            continue;
  //          }
  //
  //          DietDocument analyzeDiet = new DietDocument(diet);
  //          analyzeDiet.setFormulateOptimization(null);
  //          diet.setAnalyzeOptimization(null);
  //
  //          configureOptimizationsForResponse(diet, analyzeDiet);
  //
  //          tempDiets.add(analyzeDiet);
  //        }
  //        if (!tempDiets.isEmpty()) {
  //          diets.forEach(t -> maxSite.getMaxDiets().add(new MaxDiet(t)));
  //        }
  //
  //        maxSites.add(maxSite);
  //      }
  //
  //    } catch (Exception e) {
  //      log.error(e.getMessage());
  //      throw new CustomDEExceptions(
  //          "An error has occurred while processing the request. Please try later.",
  //          HttpStatus.BAD_REQUEST.value());
  //    }
  //    return maxSites;
  //  }

  //  private List<PenDocument> setupSitePens(List<Pens> allPens, Sites site) {
  //
  //    List<PenDocument> pens =
  //        allPens.stream().map(Pens::getPenDocument).toList().parallelStream()
  //            .filter(p ->
  // p.getSiteId().toString().equals(site.getSiteDocument().getId().toString()))
  //            .toList();
  //
  //    for (PenDocument pen : pens) {
  //      if (pen.getOptimizationId() == null) {
  //        pen.setOptimizationId(Optimization.DefaultAnalyzeOptimizationId.getValue());
  //      }
  //    }
  //
  //    return pens;
  //  }

  //  private void setBarnsForMaxSite(List<PenDocument> pens, DairyMax maxSite) {
  //
  //    PenDocument penForBarnInfo = pens.stream().findFirst().orElse(null);
  //
  //    maxSite.setMaxBarns(new ArrayList<>());
  //    if (penForBarnInfo != null) {
  //      maxSite
  //          .getMaxBarns()
  //          .add(
  //              new MaxBarn(
  //                  pens,
  //                  penForBarnInfo.getBarnId(),
  //                  penForBarnInfo.getBarn(),
  //                  penForBarnInfo.getCreateUser()));
  //    }
  //  }
  //
  //  private List<DietDocument> initializeSiteDiets(
  //      List<PenDocument> pens, Sites site, List<Diets> allDiets) {
  //
  //    List<DietDocument> diets =
  //        new ArrayList<>(
  //            allDiets.stream().map(Diets::getDietDocument).toList().parallelStream()
  //                .filter(
  //                    d ->
  // d.getSiteId().toString().equals(site.getSiteDocument().getId().toString()))
  //                .toList());
  //    diets.removeIf(
  //        d ->
  //            d.getIsSystemGenerated()
  //                && pens.stream()
  //                    .noneMatch(
  //                        p ->
  //                            p.getDietId() != null
  //                                && p.getDietId().toString().equals(d.getId().toString())));
  //
  //    return diets;
  //  }

  //  private void configureOptimizationsForResponse(DietDocument diet, DietDocument analyzeDiet) {
  //
  //    if (diet.getSource() == DietSource.MAX && diet.getFormulateOptimization() != null) {
  //      diet.setOptimizationId(Optimization.DefaultFormulateOptimizationId.getValue());
  //      diet.setOptimizationType(
  //          OptimizationType.DefaultFormulateOptimizationType.getOptimizationType());
  //      diet.setOptimizationStatus(diet.getFormulateOptimization().getStatus().toString());
  //    } else if (diet.getSource() == DietSource.USER_CREATED
  //        && diet.getFormulateOptimization() != null) {
  //      diet.setOptimizationId(Optimization.DefaultFormulateOptimizationId.getValue());
  //      diet.setOptimizationType(
  //          OptimizationType.DefaultFormulateOptimizationType.getOptimizationType());
  //      diet.setOptimizationStatus("NA");
  //    }
  //
  //    if (analyzeDiet.getSource() == DietSource.MAX && analyzeDiet.getAnalyzeOptimization() !=
  // null) {
  //      analyzeDiet.setOptimizationId(Optimization.DefaultAnalyzeOptimizationId.getValue());
  //      analyzeDiet.setOptimizationType(
  //          OptimizationType.DefaultFormulateOptimizationType.getOptimizationType());
  //      analyzeDiet.setOptimizationStatus(
  //          CaseFormat.UPPER_UNDERSCORE.to(
  //              CaseFormat.UPPER_CAMEL, analyzeDiet.getAnalyzeOptimization().getStatus().name()));
  //    } else if (analyzeDiet.getSource() == DietSource.USER_CREATED
  //        && analyzeDiet.getAnalyzeOptimization() == null) {
  //      analyzeDiet.setOptimizationId(Optimization.DefaultAnalyzeOptimizationId.getValue());
  //      analyzeDiet.setOptimizationType(
  //          OptimizationType.DefaultFormulateOptimizationType.getOptimizationType());
  //      analyzeDiet.setOptimizationStatus("NA");
  //    }
  //  }

  private void processDiets(
      List<Diets> dbDiets, DairyMax max, Accounts account, Sites site, List<Pens> pens)
      throws CustomDEExceptions {
    deleteDBDiets(dbDiets, max, pens);
    processMaxDiets(dbDiets, max, account, site, pens);
  }

  private void deleteDBDiets(List<Diets> dbDiets, DairyMax max, List<Pens> pens)
      throws CustomDEExceptions {
    log.debug("[DairyMaxServiceImpl][deleteDBDiets] Entering...");
    try {
      dbDiets.forEach(
          dd -> {
            if (dd.getDietDocument().getSource().equals(DietSource.MAX)
                && max.getMaxDiets().parallelStream()
                    .noneMatch(md -> Objects.equals(md.getMaxId(), dd.getDietDocument().getId()))
                && !dd.isDeleted()) {
              log.debug(
                  "[DairyMaxServiceImpl][deleteDBDiets] Deleting DB diet: "
                      + dd.getDietDocument().getId());
              dd.setDeleted(true);
              dd.getDietDocument().setIsDeleted(true);
              // dd.setUpdatedDate(new Date());
              dietRepository.saveAndFlush(dd);

              Pens pen =
                  pens.stream()
                      .filter(
                          p ->
                              p.getPenDocument().getDietId() != null
                                  && p.getPenDocument()
                                      .getDietId()
                                      .equals(dd.getDietDocument().getId()))
                      .findFirst()
                      .orElse(null);

              if (pen != null) {
                pen.getPenDocument().setDietId(null);
                penRepository.saveAndFlush(pen);
              }
            }
          });
    } catch (Exception e) {
      log.error("[DairyMaxServiceImpl][deleteDBDiets] " + e.getMessage());
      throw new CustomDEExceptions(e.getMessage(), HttpStatus.BAD_REQUEST.value());
    }
  }

  private void processMaxDiets(
      List<Diets> dbDiets, DairyMax max, Accounts account, Sites site, List<Pens> pens)
      throws CustomDEExceptions {
    log.debug("[DairyMaxServiceImpl][processMaxDiets] Entering...");
    try {
      List<Diets> dietsToAddUpdate = new ArrayList<>();
      for (MaxDiet maxDiet : max.getMaxDiets()) {
        if (maxDiet.getMaxAnalyzeOptimization() != null
            && maxDiet.getMaxAnalyzeOptimization().getNutrients() != null) {

          List<DietOptimizationNutrient> nutrients =
              new ArrayList<>(maxDiet.getMaxAnalyzeOptimization().getNutrients());

          nutrients.removeIf(
              n ->
                  n.getNutrientSpeciesId() != null
                      && !n.getNutrientSpeciesId()
                          .equals(Nutrients.DEFAULT_NEL_DAI_KG.getNutrientValue()));

          maxDiet.getMaxAnalyzeOptimization().setNutrients(nutrients);

          log.debug(
              "[DairyMaxServiceImpl][processMaxDiets] Analyze optimization nutrients removed.");
        }
        if (maxDiet.getMaxFormulateOptimization() != null
            && maxDiet.getMaxFormulateOptimization().getNutrients() != null) {

          List<DietOptimizationNutrient> nutrients =
              new ArrayList<>(maxDiet.getMaxFormulateOptimization().getNutrients());

          nutrients.removeIf(
              n ->
                  n.getNutrientSpeciesId() != null
                      && !n.getNutrientSpeciesId()
                          .equals(Nutrients.DEFAULT_NEL_DAI_KG.getNutrientValue()));

          maxDiet.getMaxFormulateOptimization().setNutrients(nutrients);

          log.debug(
              "[DairyMaxServiceImpl][processMaxDiets] Formulate optimization nutrients removed.");
        }
        maxDiet.setMaxLabyrinthAccountId(account.getAccountDocument().getId());
        maxDiet.setMaxSiteId(site.getSiteDocument().getId());
        Diets updatedDiet =
            dbDiets.stream()
                .filter(
                    d ->
                        d.getDietDocument().getId() != null
                            && d.getDietDocument().getId().equals(maxDiet.getMaxId()))
                .findFirst()
                .orElse(null);
        if (updatedDiet != null) {
          log.debug(
              "[DairyMaxServiceImpl][processMaxDiets] Updating diet "
                  + updatedDiet.getDietDocument().getId());
          updatedDiet.setDeleted(false);
          updatedDiet.setDietDocument(new DietDocument(maxDiet));
          updatedDiet.getDietDocument().setIsDeleted(false);
          dietsToAddUpdate.add(updatedDiet);
          updatePenForInvalidDiet(pens, updatedDiet);
        } else {
          log.debug("[DairyMaxServiceImpl][processMaxDiets] Adding diet " + maxDiet.getMaxId());
          Diets newDiet = new Diets();
          newDiet.setDietDocument(new DietDocument(maxDiet));
          dietsToAddUpdate.add(newDiet);
        }
      }
      dietRepository.saveAllAndFlush(dietsToAddUpdate);

    } catch (Exception e) {
      log.error("[DairyMaxServiceImpl][processMaxDiets] " + e.getMessage());
      throw new CustomDEExceptions(e.getMessage(), HttpStatus.BAD_REQUEST.value());
    }
  }

  private void updatePenForInvalidDiet(List<Pens> pens, Diets diet) {

    pens.stream()
        .filter(
            x ->
                x.getPenDocument().getDietId() != null
                    && x.getPenDocument().getDietId().equals(diet.getDietDocument().getId()))
        .forEach(
            pen -> {
              Boolean isValidDiet =
                  pensService.dietValidation(diet, pen.getPenDocument().getOptimizationType());
              pensService.saveAndFlushPen(pen, isValidDiet);
            });
  }
}
