/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DateEpoch implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Date")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  public Instant date;

  @JsonProperty("Epoch")
  public Long epoch;

  public DateEpoch(Instant date) {
    this.date = date;
    this.epoch = date.getEpochSecond();
  }

  public Long getEpoch() {
    if (date != null) {
      return date.getEpochSecond();
    }
    return epoch;
  }
}
