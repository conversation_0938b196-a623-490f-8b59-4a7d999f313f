import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ecsPatterns from 'aws-cdk-lib/aws-ecs-patterns';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

export interface DairyEnteligenStackProps extends cdk.StackProps {
  environment: 'dev' | 'stage' | 'prod';
}

export class DairyEnteligenStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: DairyEnteligenStackProps) {
    super(scope, id, props);

    const { environment } = props;

    // VPC - Your network foundation
    const vpc = new ec2.Vpc(this, `DairyEnteligen-VPC-${environment}`, {
      maxAzs: 2,
      natGateways: environment === 'prod' ? 2 : 1,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 28,
          name: 'database',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
    });

    // S3 Bucket - Replaces your current S3 storage
    const s3Bucket = new s3.Bucket(this, `DairyEnteligen-S3-${environment}`, {
      bucketName: `dairy-enteligen-${environment}-${this.account}`,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    });

    // Database - Replaces your PostgreSQL setup
    const dbSecret = new secretsmanager.Secret(this, `DB-Secret-${environment}`, {
      generateSecretString: {
        secretStringTemplate: JSON.stringify({ username: 'admin' }),
        generateStringKey: 'password',
        excludeCharacters: '"@/\\',
      },
    });

    const database = new rds.DatabaseInstance(this, `DairyEnteligen-DB-${environment}`, {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_15_4,
      }),
      instanceType: environment === 'prod' 
        ? ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM)
        : ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MICRO),
      credentials: rds.Credentials.fromSecret(dbSecret),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
      databaseName: 'cargill',
      backupRetention: environment === 'prod' ? cdk.Duration.days(7) : cdk.Duration.days(1),
      deletionProtection: environment === 'prod',
      removalPolicy: environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    });

    // ECS Cluster - Replaces your Kubernetes cluster
    const cluster = new ecs.Cluster(this, `DairyEnteligen-Cluster-${environment}`, {
      vpc,
      containerInsights: true,
    });

    // Task Role - Permissions for your application
    const taskRole = new iam.Role(this, `DairyEnteligen-TaskRole-${environment}`, {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      inlinePolicies: {
        S3Access: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
                's3:ListBucket',
              ],
              resources: [
                s3Bucket.bucketArn,
                `${s3Bucket.bucketArn}/*`,
              ],
            }),
          ],
        }),
        SecretsAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'secretsmanager:GetSecretValue',
              ],
              resources: [dbSecret.secretArn],
            }),
          ],
        }),
      },
    });

    // Fargate Service - Replaces your Kubernetes deployment
    const fargateService = new ecsPatterns.ApplicationLoadBalancedFargateService(
      this,
      `DairyEnteligen-Service-${environment}`,
      {
        cluster,
        cpu: environment === 'prod' ? 1024 : 512,
        memoryLimitMiB: environment === 'prod' ? 2048 : 1024,
        desiredCount: environment === 'prod' ? 2 : 1,
        taskImageOptions: {
          image: ecs.ContainerImage.fromRegistry('your-account.dkr.ecr.us-east-1.amazonaws.com/dairyenteligen-service-api:latest'),
          containerPort: 8080,
          taskRole,
          environment: {
            SPRING_PROFILES_ACTIVE: environment,
            AWS_DEFAULT_REGION: this.region,
            BUCKET_DEDISCOVER_NAME: s3Bucket.bucketName,
          },
          secrets: {
            DB_CONNECTION: ecs.Secret.fromSecretsManager(dbSecret, 'host'),
            DB_USERNAME: ecs.Secret.fromSecretsManager(dbSecret, 'username'),
            DB_PASSWORD: ecs.Secret.fromSecretsManager(dbSecret, 'password'),
          },
        },
        publicLoadBalancer: true,
        domainName: `dairyenteligen-${environment}.yourdomain.com`,
        domainZone: undefined, // You'll need to set up Route53 hosted zone
      }
    );

    // Health Check - Replaces your Captain healthcheck
    fargateService.targetGroup.configureHealthCheck({
      path: '/diagnostics/heartbeat',
      healthyHttpCodes: '200',
    });

    // Auto Scaling - Production scaling
    if (environment === 'prod') {
      const scaling = fargateService.service.autoScaleTaskCount({
        minCapacity: 2,
        maxCapacity: 10,
      });

      scaling.scaleOnCpuUtilization('CpuScaling', {
        targetUtilizationPercent: 70,
      });
    }

    // Outputs
    new cdk.CfnOutput(this, 'LoadBalancerDNS', {
      value: fargateService.loadBalancer.loadBalancerDnsName,
    });

    new cdk.CfnOutput(this, 'DatabaseEndpoint', {
      value: database.instanceEndpoint.hostname,
    });

    new cdk.CfnOutput(this, 'S3BucketName', {
      value: s3Bucket.bucketName,
    });
  }
}
