#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { DairyEnteligenStack } from './dairy-enteligen-stack';

const app = new cdk.App();

// Development Environment
new DairyEnteligenStack(app, 'DairyEnteligen-Dev', {
  environment: 'dev',
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: 'us-east-1',
  },
  tags: {
    Environment: 'dev',
    Project: 'DairyEnteligen',
    Team: 'dairyenteligenfoliothree',
  },
});

// Staging Environment
new DairyEnteligenStack(app, 'DairyEnteligen-Stage', {
  environment: 'stage',
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: 'us-east-1',
  },
  tags: {
    Environment: 'stage',
    Project: 'DairyEnteligen',
    Team: 'dairyenteligenfoliothree',
  },
});

// Production Environment
new DairyEnteligenStack(app, 'DairyEnteligen-Prod', {
  environment: 'prod',
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: 'us-east-1',
  },
  tags: {
    Environment: 'prod',
    Project: 'DairyEnteligen',
    Team: 'dairyenteligenfoliothree',
  },
});
