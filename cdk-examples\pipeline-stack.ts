import * as cdk from 'aws-cdk-lib';
import * as codepipeline from 'aws-cdk-lib/aws-codepipeline';
import * as codepipelineActions from 'aws-cdk-lib/aws-codepipeline-actions';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';

export class DairyEnteligenPipelineStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // ECR Repository - Replaces Cargill's Docker registry
    const ecrRepo = new ecr.Repository(this, 'DairyEnteligenECR', {
      repositoryName: 'dairyenteligen-service-api',
      imageScanOnPush: true,
      lifecycleRules: [
        {
          maxImageCount: 10,
          tagStatus: ecr.TagStatus.UNTAGGED,
        },
      ],
    });

    // S3 Bucket for Pipeline Artifacts
    const artifactsBucket = new s3.Bucket(this, 'PipelineArtifacts', {
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // CodeBuild Project - Replaces your Maven builds in Vela
    const buildProject = new codebuild.Project(this, 'DairyEnteligenBuild', {
      projectName: 'dairy-enteligen-build',
      source: codebuild.Source.gitHub({
        owner: 'your-github-org',
        repo: 'dairyenteligen_serviceapis',
        webhook: true,
        webhookFilters: [
          codebuild.FilterGroup.inEventOf(codebuild.EventAction.PUSH)
            .andBranchIs('develop')
            .andBranchIs('stage')
            .andBranchIs('main'),
          codebuild.FilterGroup.inEventOf(codebuild.EventAction.PULL_REQUEST_CREATED),
        ],
      }),
      environment: {
        buildImage: codebuild.LinuxBuildImage.STANDARD_7_0,
        privileged: true, // Required for Docker builds
        computeType: codebuild.ComputeType.MEDIUM,
        environmentVariables: {
          AWS_DEFAULT_REGION: {
            value: this.region,
          },
          AWS_ACCOUNT_ID: {
            value: this.account,
          },
          IMAGE_REPO_NAME: {
            value: ecrRepo.repositoryName,
          },
          IMAGE_URI: {
            value: ecrRepo.repositoryUri,
          },
        },
      },
      buildSpec: codebuild.BuildSpec.fromObject({
        version: '0.2',
        phases: {
          pre_build: {
            commands: [
              'echo Logging in to Amazon ECR...',
              'aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com',
              'echo Setting up environment...',
              'export COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)',
              'export IMAGE_TAG=${COMMIT_HASH:=latest}',
            ],
          },
          build: {
            commands: [
              'echo Build started on `date`',
              'echo Building Maven project...',
              'mvn clean install -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn',
              'echo Running code quality checks...',
              'mvn spotless:check -B',
              'echo Building Docker image...',
              'docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .',
              'docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $IMAGE_URI:$IMAGE_TAG',
              'docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $IMAGE_URI:latest',
            ],
          },
          post_build: {
            commands: [
              'echo Build completed on `date`',
              'echo Pushing Docker image...',
              'docker push $IMAGE_URI:$IMAGE_TAG',
              'docker push $IMAGE_URI:latest',
              'echo Writing image definitions file...',
              'printf \'[{"name":"dairyenteligen-service-api","imageUri":"%s"}]\' $IMAGE_URI:$IMAGE_TAG > imagedefinitions.json',
            ],
          },
        },
        artifacts: {
          files: [
            'imagedefinitions.json',
            'cdk-examples/**/*',
          ],
        },
      }),
    });

    // Grant ECR permissions to CodeBuild
    ecrRepo.grantPullPush(buildProject);

    // CodeBuild for CDK Deploy
    const cdkDeployProject = new codebuild.Project(this, 'CDKDeployProject', {
      projectName: 'dairy-enteligen-cdk-deploy',
      source: codebuild.Source.s3({
        bucket: artifactsBucket,
        path: 'source-output',
      }),
      environment: {
        buildImage: codebuild.LinuxBuildImage.STANDARD_7_0,
        computeType: codebuild.ComputeType.SMALL,
      },
      buildSpec: codebuild.BuildSpec.fromObject({
        version: '0.2',
        phases: {
          install: {
            'runtime-versions': {
              nodejs: '18',
            },
            commands: [
              'cd cdk-examples',
              'npm install',
              'npm install -g aws-cdk',
            ],
          },
          build: {
            commands: [
              'echo "Deploying CDK stack for $ENVIRONMENT"',
              'cdk deploy DairyEnteligen-$ENVIRONMENT --require-approval never',
            ],
          },
        },
      }),
    });

    // Grant CDK deploy permissions
    cdkDeployProject.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'cloudformation:*',
          'ecs:*',
          'ec2:*',
          'rds:*',
          's3:*',
          'iam:*',
          'logs:*',
          'secretsmanager:*',
          'elasticloadbalancing:*',
        ],
        resources: ['*'],
      })
    );

    // Pipeline Artifacts
    const sourceOutput = new codepipeline.Artifact();
    const buildOutput = new codepipeline.Artifact();

    // CodePipeline - Replaces your Vela pipeline
    const pipeline = new codepipeline.Pipeline(this, 'DairyEnteligenPipeline', {
      pipelineName: 'dairy-enteligen-pipeline',
      artifactBucket: artifactsBucket,
      stages: [
        {
          stageName: 'Source',
          actions: [
            new codepipelineActions.GitHubSourceAction({
              actionName: 'GitHub_Source',
              owner: 'your-github-org',
              repo: 'dairyenteligen_serviceapis',
              branch: 'develop',
              oauthToken: cdk.SecretValue.secretsManager('github-token'),
              output: sourceOutput,
            }),
          ],
        },
        {
          stageName: 'Build',
          actions: [
            new codepipelineActions.CodeBuildAction({
              actionName: 'Build_and_Test',
              project: buildProject,
              input: sourceOutput,
              outputs: [buildOutput],
            }),
          ],
        },
        {
          stageName: 'Deploy_Dev',
          actions: [
            new codepipelineActions.CodeBuildAction({
              actionName: 'Deploy_to_Dev',
              project: cdkDeployProject,
              input: buildOutput,
              environmentVariables: {
                ENVIRONMENT: {
                  value: 'Dev',
                },
              },
            }),
          ],
        },
        // Add manual approval for production
        {
          stageName: 'Approval',
          actions: [
            new codepipelineActions.ManualApprovalAction({
              actionName: 'Manual_Approval',
              additionalInformation: 'Please review and approve deployment to production',
            }),
          ],
        },
        {
          stageName: 'Deploy_Prod',
          actions: [
            new codepipelineActions.CodeBuildAction({
              actionName: 'Deploy_to_Prod',
              project: cdkDeployProject,
              input: buildOutput,
              environmentVariables: {
                ENVIRONMENT: {
                  value: 'Prod',
                },
              },
            }),
          ],
        },
      ],
    });

    // Outputs
    new cdk.CfnOutput(this, 'ECRRepositoryURI', {
      value: ecrRepo.repositoryUri,
    });

    new cdk.CfnOutput(this, 'PipelineName', {
      value: pipeline.pipelineName,
    });
  }
}
