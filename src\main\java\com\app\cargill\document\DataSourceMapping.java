/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@ToString
public class DataSourceMapping implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("SystemName")
  private String systemName;

  @JsonProperty("SystemId")
  private String systemId;

  @JsonProperty("ExternalSystemKey")
  private String externalSystemKey = null;
}
